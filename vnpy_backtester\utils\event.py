"""
Event-related classes and functions for event-driven architecture.
"""

from enum import Enum
from queue import Queue, Empty
from threading import Thread
from time import sleep
from typing import Any, Callable, List, Optional

# 定义事件类型


class EventType(Enum):
    """
    Event types used in the event-driven architecture.
    """
    EVENT_TICK = "eTick."
    EVENT_BAR = "eBar."
    EVENT_ORDER = "eOrder."
    EVENT_TRADE = "eTrade."
    EVENT_POSITION = "ePosition."
    EVENT_ACCOUNT = "eAccount."
    EVENT_CONTRACT = "eContract."
    EVENT_LOG = "eLog."
    EVENT_TIMER = "eTimer."
    EVENT_INIT = "eInit."
    EVENT_START = "eStart."
    EVENT_STOP = "eStop."
    EVENT_STOP_ORDER = "eStopOrder."

    # 新增事件类型
    EVENT_ACCOUNT_UPDATE = "eAccountUpdate."  # 账户更新事件
    EVENT_STATISTICS_UPDATE = "eStatisticsUpdate."  # 统计指标更新事件
    EVENT_CHART_UPDATE = "eChartUpdate."  # 图表更新事件
    EVENT_EQUITY_UPDATE = "eEquityUpdate."  # 资金曲线更新事件
    EVENT_DRAWDOWN_UPDATE = "eDrawdownUpdate."  # 回撤更新事件
    EVENT_PERFORMANCE_UPDATE = "ePerformanceUpdate."  # 绩效指标更新事件


class Event:
    """
    Event object consists of type and data.
    """

    def __init__(self, type: EventType, data: Any = None) -> None:
        """
        Initialize an Event.

        Parameters:
            type: Event type
            data: Event data object
        """
        self.type: EventType = type
        self.data: Any = data


# 定义处理器类型
HandlerType = Callable[[Event], None]


class EventEngine:
    """
    Event engine distributes event objects based on their type.

    It is the core of the event-driven architecture.
    """

    def __init__(self, interval: int = 1) -> None:
        """
        Initialize an EventEngine.

        Parameters:
            interval: Event processing interval in milliseconds
        """
        self._interval: int = interval
        self._queue: Queue = Queue()
        self._active: bool = False
        self._thread: Thread = Thread(target=self._run)
        self._timer: Thread = Thread(target=self._run_timer)
        self._handlers: dict = {}
        self._general_handlers: List = []

    def _run(self) -> None:
        """
        Run event processing loop.
        """
        while self._active:
            try:
                event = self._queue.get(block=True, timeout=0.1)
                self._process(event)
            except Empty:
                pass

    def _process(self, event: Event) -> None:
        """
        Process an event.
        """
        # 处理特定类型的事件处理器
        if event.type in self._handlers:
            [handler(event) for handler in self._handlers[event.type]]

        # 处理通用事件处理器
        if self._general_handlers:
            [handler(event) for handler in self._general_handlers]

    def _run_timer(self) -> None:
        """
        Run timer event loop.
        """
        while self._active:
            sleep(self._interval / 1000)
            event = Event(EventType.EVENT_TIMER)
            self.put(event)

    def start(self) -> None:
        """
        Start event engine.
        """
        self._active = True
        self._thread.start()
        self._timer.start()

    def stop(self) -> None:
        """
        Stop event engine.
        """
        self._active = False
        self._timer.join()
        self._thread.join()

    def put(self, event: Event) -> None:
        """
        Put an event into event queue.
        For backtesting, process the event immediately to ensure synchronous behavior.
        """
        # 在回测环境中，直接处理事件，而不是放入队列
        self._process(event)

    def register(self, type: EventType, handler: HandlerType) -> None:
        """
        Register a handler for a specific event type.
        """
        handler_list = self._handlers.setdefault(type, [])
        if handler not in handler_list:
            handler_list.append(handler)

    def unregister(self, type: EventType, handler: HandlerType) -> None:
        """
        Unregister a handler for a specific event type.
        """
        handler_list = self._handlers.get(type, [])
        if handler in handler_list:
            handler_list.remove(handler)
        if not handler_list:
            self._handlers.pop(type, None)

    def register_general(self, handler: HandlerType) -> None:
        """
        Register a handler for all event types.
        """
        if handler not in self._general_handlers:
            self._general_handlers.append(handler)

    def unregister_general(self, handler: HandlerType) -> None:
        """
        Unregister a general handler.
        """
        if handler in self._general_handlers:
            self._general_handlers.remove(handler)

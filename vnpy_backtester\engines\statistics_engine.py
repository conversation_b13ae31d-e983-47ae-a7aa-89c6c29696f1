"""
统计指标引擎 - 基于事件驱动架构
负责实时计算和更新各种统计指标
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import numpy as np
import pandas as pd

from vnpy_backtester.utils.event import Event, EventEngine, EventType
from vnpy_backtester.engines.account_engine import AccountData, AccountEngine
from vnpy_backtester.objects.object import TradeData


@dataclass
class StatisticsData:
    """统计指标数据类"""
    datetime: datetime
    
    # 基础指标
    total_return: float = 0.0  # 总收益率
    annual_return: float = 0.0  # 年化收益率
    max_drawdown: float = 0.0  # 最大回撤
    max_drawdown_percent: float = 0.0  # 最大回撤百分比
    sharpe_ratio: float = 0.0  # 夏普比率
    
    # 交易指标
    total_trades: int = 0  # 总交易次数
    winning_trades: int = 0  # 盈利交易次数
    losing_trades: int = 0  # 亏损交易次数
    win_rate: float = 0.0  # 胜率
    profit_ratio: float = 0.0  # 盈亏比
    profit_factor: float = 0.0  # 利润因子
    
    # 盈亏指标
    avg_winning: float = 0.0  # 平均盈利
    avg_losing: float = 0.0  # 平均亏损
    max_winning: float = 0.0  # 最大盈利
    max_losing: float = 0.0  # 最大亏损
    
    # 时间指标
    start_date: datetime = None
    end_date: datetime = None
    total_days: int = 0
    profit_days: int = 0
    loss_days: int = 0


class StatisticsEngine:
    """
    统计指标引擎
    基于事件驱动架构，实时计算各种统计指标
    """

    def __init__(self, event_engine: EventEngine, account_engine: AccountEngine, 
                 annual_days: int = 365, risk_free_rate: float = 0.0):
        """
        初始化统计引擎
        
        参数:
        event_engine: 事件引擎
        account_engine: 账户引擎
        annual_days: 年化天数
        risk_free_rate: 无风险利率
        """
        self.event_engine = event_engine
        self.account_engine = account_engine
        self.annual_days = annual_days
        self.risk_free_rate = risk_free_rate
        
        # 统计数据
        self.statistics_data = StatisticsData(datetime=datetime.now())
        
        # 历史统计数据
        self.statistics_history: List[StatisticsData] = []
        
        # 计算用的临时数据
        self.daily_returns: List[float] = []
        self.equity_curve: List[float] = []
        self.drawdown_curve: List[float] = []
        self.peak_equity = 0.0
        
        # 交易分析数据
        self.trade_pnls: List[float] = []
        self.winning_pnls: List[float] = []
        self.losing_pnls: List[float] = []
        
        # 注册事件处理器
        self.register_event_handlers()

    def register_event_handlers(self):
        """注册事件处理器"""
        self.event_engine.register(EventType.EVENT_ACCOUNT_UPDATE, self.process_account_update_event)
        self.event_engine.register(EventType.EVENT_TRADE, self.process_trade_event)
        self.event_engine.register(EventType.EVENT_INIT, self.process_init_event)
        self.event_engine.register(EventType.EVENT_STOP, self.process_stop_event)

    def process_init_event(self, event: Event):
        """处理初始化事件"""
        self.statistics_data = StatisticsData(datetime=datetime.now())
        self.statistics_history.clear()
        self.daily_returns.clear()
        self.equity_curve.clear()
        self.drawdown_curve.clear()
        self.trade_pnls.clear()
        self.winning_pnls.clear()
        self.losing_pnls.clear()
        self.peak_equity = self.account_engine.initial_capital

    def process_account_update_event(self, event: Event):
        """处理账户更新事件"""
        account_data: AccountData = event.data
        
        # 更新时间
        self.statistics_data.datetime = account_data.datetime
        
        # 更新资金曲线
        self.equity_curve.append(account_data.equity)
        
        # 计算日收益率
        if len(self.equity_curve) > 1:
            daily_return = (account_data.equity - self.equity_curve[-2]) / self.equity_curve[-2]
            self.daily_returns.append(daily_return)
        
        # 更新回撤
        self.update_drawdown(account_data.equity)
        
        # 计算基础指标
        self.calculate_basic_statistics(account_data)
        
        # 发送统计更新事件
        self.send_statistics_update_event()

    def process_trade_event(self, event: Event):
        """处理交易事件"""
        trade: TradeData = event.data
        
        # 如果是平仓交易，计算交易盈亏
        if trade.offset.value == "平":
            # 这里需要从账户引擎获取实际的交易盈亏
            # 简化处理，后续可以优化
            pass

    def process_stop_event(self, event: Event):
        """处理停止事件，计算最终统计指标"""
        self.calculate_final_statistics()
        self.send_statistics_update_event()

    def update_drawdown(self, current_equity: float):
        """更新回撤"""
        # 更新峰值
        if current_equity > self.peak_equity:
            self.peak_equity = current_equity
        
        # 计算当前回撤
        drawdown = self.peak_equity - current_equity
        drawdown_percent = drawdown / self.peak_equity * 100 if self.peak_equity > 0 else 0
        
        self.drawdown_curve.append(drawdown)
        
        # 更新最大回撤
        if drawdown > self.statistics_data.max_drawdown:
            self.statistics_data.max_drawdown = drawdown
        
        if drawdown_percent > self.statistics_data.max_drawdown_percent:
            self.statistics_data.max_drawdown_percent = drawdown_percent

    def calculate_basic_statistics(self, account_data: AccountData):
        """计算基础统计指标"""
        if not self.equity_curve:
            return
        
        initial_capital = self.account_engine.initial_capital
        current_equity = account_data.equity
        
        # 总收益率
        self.statistics_data.total_return = (current_equity - initial_capital) / initial_capital * 100
        
        # 年化收益率
        if len(self.daily_returns) > 0:
            avg_daily_return = np.mean(self.daily_returns)
            self.statistics_data.annual_return = avg_daily_return * self.annual_days * 100
        
        # 夏普比率
        if len(self.daily_returns) > 1:
            returns_std = np.std(self.daily_returns, ddof=1)
            if returns_std > 0:
                avg_return = np.mean(self.daily_returns)
                excess_return = avg_return - self.risk_free_rate / self.annual_days
                self.statistics_data.sharpe_ratio = excess_return / returns_std * np.sqrt(self.annual_days)

    def calculate_trade_statistics(self):
        """计算交易统计指标"""
        trades = self.account_engine.trades
        
        if not trades:
            return
        
        # 统计交易次数（只统计平仓交易）
        close_trades = [t for t in trades if t.offset.value == "平"]
        self.statistics_data.total_trades = len(close_trades)
        
        if self.statistics_data.total_trades == 0:
            return
        
        # 计算交易盈亏（简化处理）
        # 实际应该从账户引擎获取详细的交易盈亏数据
        winning_trades = 0
        losing_trades = 0
        total_profit = 0.0
        total_loss = 0.0
        
        # 这里需要更复杂的逻辑来计算每笔交易的实际盈亏
        # 暂时使用简化版本
        
        self.statistics_data.winning_trades = winning_trades
        self.statistics_data.losing_trades = losing_trades
        
        if self.statistics_data.total_trades > 0:
            self.statistics_data.win_rate = winning_trades / self.statistics_data.total_trades * 100
        
        # 盈亏比
        if total_loss != 0:
            self.statistics_data.profit_ratio = abs(total_profit / total_loss)
        
        # 利润因子
        if total_loss != 0:
            self.statistics_data.profit_factor = total_profit / abs(total_loss)

    def calculate_time_statistics(self):
        """计算时间相关统计指标"""
        account_history = self.account_engine.get_account_history()
        
        if not account_history:
            return
        
        self.statistics_data.start_date = account_history[0].datetime
        self.statistics_data.end_date = account_history[-1].datetime
        
        # 计算总天数
        if self.statistics_data.start_date and self.statistics_data.end_date:
            delta = self.statistics_data.end_date - self.statistics_data.start_date
            self.statistics_data.total_days = delta.days
        
        # 计算盈利天数和亏损天数
        profit_days = 0
        loss_days = 0
        
        for i in range(1, len(account_history)):
            prev_equity = account_history[i-1].equity
            curr_equity = account_history[i].equity
            
            if curr_equity > prev_equity:
                profit_days += 1
            elif curr_equity < prev_equity:
                loss_days += 1
        
        self.statistics_data.profit_days = profit_days
        self.statistics_data.loss_days = loss_days

    def calculate_final_statistics(self):
        """计算最终统计指标"""
        self.calculate_trade_statistics()
        self.calculate_time_statistics()

    def send_statistics_update_event(self):
        """发送统计更新事件"""
        event = Event(EventType.EVENT_STATISTICS_UPDATE, self.statistics_data)
        self.event_engine.put(event)

    def get_statistics_data(self) -> StatisticsData:
        """获取当前统计数据"""
        return self.statistics_data

    def get_statistics_dict(self) -> Dict[str, Any]:
        """获取统计数据字典格式"""
        return {
            'total_return': self.statistics_data.total_return,
            'annual_return': self.statistics_data.annual_return,
            'max_drawdown': self.statistics_data.max_drawdown,
            'max_drawdown_percent': self.statistics_data.max_drawdown_percent,
            'sharpe_ratio': self.statistics_data.sharpe_ratio,
            'total_trades': self.statistics_data.total_trades,
            'winning_trades': self.statistics_data.winning_trades,
            'losing_trades': self.statistics_data.losing_trades,
            'win_rate': self.statistics_data.win_rate,
            'profit_ratio': self.statistics_data.profit_ratio,
            'profit_factor': self.statistics_data.profit_factor,
            'start_date': self.statistics_data.start_date,
            'end_date': self.statistics_data.end_date,
            'total_days': self.statistics_data.total_days,
            'profit_days': self.statistics_data.profit_days,
            'loss_days': self.statistics_data.loss_days,
            'capital': self.account_engine.initial_capital,
            'end_balance': self.account_engine.account_data.equity
        }

    def get_equity_curve(self) -> List[float]:
        """获取资金曲线"""
        return self.equity_curve

    def get_drawdown_curve(self) -> List[float]:
        """获取回撤曲线"""
        return self.drawdown_curve

    def get_daily_returns(self) -> List[float]:
        """获取日收益率"""
        return self.daily_returns

"""
测试事件驱动引擎功能
验证账户管理、统计指标、图表生成等功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from vnpy_backtester.engines.engine import BacktestingEngine
from vnpy_backtester.objects.object import BarData
from vnpy_backtester.utils.constant import Exchange
from vnpy_backtester.strategies.multi_position_strategy import MultiPositionStrategy


def create_test_data(num_bars=1000):
    """创建测试数据"""
    print("创建测试数据...")
    
    # 生成时间序列
    start_date = datetime(2023, 1, 1)
    dates = [start_date + timedelta(minutes=15*i) for i in range(num_bars)]
    
    # 生成价格数据（随机游走）
    np.random.seed(42)  # 固定随机种子
    price_changes = np.random.normal(0, 0.01, num_bars)  # 1%的标准差
    prices = [100.0]  # 起始价格
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1.0))  # 确保价格不为负
    
    # 生成OHLCV数据
    bars = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        # 简单的OHLC生成
        high = close * (1 + abs(np.random.normal(0, 0.005)))
        low = close * (1 - abs(np.random.normal(0, 0.005)))
        open_price = prices[i-1] if i > 0 else close
        volume = np.random.uniform(1000, 10000)
        
        # 生成交易信号（简单的随机信号）
        if i < 50:  # 前50根K线不产生信号
            signal = 0
        else:
            # 20%概率产生信号
            rand = np.random.random()
            if rand < 0.1:
                signal = 1  # 做多
            elif rand < 0.2:
                signal = -1  # 做空
            else:
                signal = 0  # 无信号
        
        bar = BarData(
            symbol="TESTUSDT",
            exchange=Exchange.BINANCE,
            datetime=date,
            open_price=open_price,
            high_price=high,
            low_price=low,
            close_price=close,
            volume=volume,
            turnover=close * volume,
            gateway_name="BACKTEST"
        )
        
        # 添加信号属性
        setattr(bar, "signals", signal)
        bars.append(bar)
    
    print(f"生成了 {len(bars)} 根K线数据")
    return bars


def test_event_driven_engines():
    """测试事件驱动引擎"""
    print("开始测试事件驱动引擎...")
    
    # 创建测试数据
    bars = create_test_data(500)
    
    # 创建回测引擎
    engine = BacktestingEngine()
    
    # 设置参数
    engine.set_parameters(
        vt_symbol="TESTUSDT.BINANCE",
        start=bars[0].datetime,
        end=bars[-1].datetime,
        rate=0.0003,
        slippage=0.01,
        capital=50000,
        holding_bars=20,
        position_size=1000,
        order_type="amount",
        max_positions=5
    )
    
    # 添加策略
    engine.add_strategy(MultiPositionStrategy)
    
    # 加载数据
    engine.history_data = bars
    
    print("开始回测...")
    
    # 运行回测
    engine.run_backtesting()
    
    print("回测完成，开始分析结果...")
    
    # 测试账户引擎
    print("\n====== 账户引擎测试 ======")
    account_data = engine.get_account_data()
    if account_data:
        print(f"账户净值: {account_data.equity:,.2f}")
        print(f"已实现盈亏: {account_data.realized_pnl:,.2f}")
        print(f"未实现盈亏: {account_data.unrealized_pnl:,.2f}")
        print(f"累计手续费: {account_data.commission:,.2f}")
        print(f"累计滑点: {account_data.slippage:,.2f}")
        print(f"可用资金: {account_data.available:,.2f}")
        print(f"已用保证金: {account_data.margin_used:,.2f}")
        
        # 获取持仓信息
        positions = engine.account_engine.get_positions()
        print(f"当前持仓数量: {len(positions)}")
        for pos_key, position in positions.items():
            print(f"  {pos_key}: 数量={position.volume}, 价格={position.price:.2f}, 盈亏={position.pnl:.2f}")
    else:
        print("账户引擎数据获取失败")
    
    # 测试统计引擎
    print("\n====== 统计引擎测试 ======")
    try:
        stats = engine.get_statistics_from_engines()
        print(f"总收益率: {stats.get('total_return', 0):.2f}%")
        print(f"年化收益率: {stats.get('annual_return', 0):.2f}%")
        print(f"最大回撤: {stats.get('max_drawdown_percent', 0):.2f}%")
        print(f"夏普比率: {stats.get('sharpe_ratio', 0):.2f}")
        print(f"总交易次数: {stats.get('total_trades', 0)}")
        print(f"胜率: {stats.get('win_rate', 0):.2f}%")
        
        # 获取资金曲线
        equity_curve = engine.get_equity_curve()
        print(f"资金曲线数据点数: {len(equity_curve)}")
        if equity_curve:
            print(f"起始净值: {equity_curve[0]:,.2f}")
            print(f"结束净值: {equity_curve[-1]:,.2f}")
    except Exception as e:
        print(f"统计引擎测试失败: {e}")
    
    # 测试图表引擎
    print("\n====== 图表引擎测试 ======")
    try:
        # 生成图表（不显示，只保存）
        chart_path = "vnpy_backtester/charts/test_event_driven_result.html"
        fig = engine.generate_chart(
            save_path=chart_path,
            show_chart=False,
            title="事件驱动引擎测试结果"
        )
        
        if fig:
            print("图表生成成功")
            print(f"图表已保存为: {chart_path}")
            
            # 获取图表数据
            chart_data = engine.chart_engine.get_chart_data()
            print(f"图表数据点数: {len(chart_data.get('equity_curve', []))}")
            print(f"交易标记数: {len(chart_data.get('trades', []))}")
        else:
            print("图表生成失败")
    except Exception as e:
        print(f"图表引擎测试失败: {e}")
    
    # 对比传统方法
    print("\n====== 传统方法对比 ======")
    try:
        engine.calculate_result()
        traditional_stats = engine.calculate_statistics()
        print(f"传统方法 - 总收益率: {traditional_stats.get('total_return', 0):.2f}%")
        print(f"传统方法 - 最大回撤: {traditional_stats.get('max_ddpercent', 0):.2f}%")
        print(f"传统方法 - 夏普比率: {traditional_stats.get('sharpe_ratio', 0):.2f}")
    except Exception as e:
        print(f"传统方法计算失败: {e}")
    
    print("\n====== 测试完成 ======")
    return engine


if __name__ == "__main__":
    # 运行测试
    engine = test_event_driven_engines()
    
    print("\n测试总结:")
    print("1. 事件驱动架构已成功集成到回测引擎中")
    print("2. 账户管理引擎可以实时跟踪资金变化")
    print("3. 统计指标引擎可以实时计算各种指标")
    print("4. 图表引擎可以生成可视化结果")
    print("5. 所有引擎通过事件系统协调工作")
    
    # 显示事件处理统计
    if hasattr(engine.event_engine, '_handlers'):
        print(f"\n注册的事件处理器数量: {len(engine.event_engine._handlers)}")
        for event_type, handlers in engine.event_engine._handlers.items():
            print(f"  {event_type.value}: {len(handlers)} 个处理器")

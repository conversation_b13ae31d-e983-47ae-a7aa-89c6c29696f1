"""
账户管理引擎 - 基于事件驱动架构
负责实时计算和更新账户资金、持仓、盈亏等信息
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from vnpy_backtester.utils.event import Event, EventEngine, EventType
from vnpy_backtester.objects.object import TradeData, BarData
from vnpy_backtester.utils.constant import Direction


@dataclass
class AccountData:
    """账户数据类"""
    datetime: datetime
    balance: float  # 账户余额
    available: float  # 可用资金
    frozen: float  # 冻结资金
    total_pnl: float  # 总盈亏
    realized_pnl: float  # 已实现盈亏
    unrealized_pnl: float  # 未实现盈亏
    commission: float  # 累计手续费
    slippage: float  # 累计滑点
    equity: float  # 账户净值
    margin_used: float  # 已用保证金
    margin_available: float  # 可用保证金


@dataclass
class PositionData:
    """持仓数据类"""
    symbol: str
    direction: str  # "long" or "short"
    volume: float
    price: float  # 平均持仓价格
    pnl: float  # 持仓盈亏
    entry_time: datetime
    bar_count: int = 0  # 持仓K线数


class AccountEngine:
    """
    账户管理引擎
    基于事件驱动架构，实时计算和更新账户信息
    """

    def __init__(self, event_engine: EventEngine, initial_capital: float = 100000.0):
        """
        初始化账户引擎
        
        参数:
        event_engine: 事件引擎
        initial_capital: 初始资金
        """
        self.event_engine = event_engine
        self.initial_capital = initial_capital
        
        # 账户信息
        self.account_data = AccountData(
            datetime=datetime.now(),
            balance=initial_capital,
            available=initial_capital,
            frozen=0.0,
            total_pnl=0.0,
            realized_pnl=0.0,
            unrealized_pnl=0.0,
            commission=0.0,
            slippage=0.0,
            equity=initial_capital,
            margin_used=0.0,
            margin_available=initial_capital
        )
        
        # 持仓信息
        self.positions: Dict[str, PositionData] = {}
        
        # 交易记录
        self.trades: List[TradeData] = []
        
        # 历史账户数据
        self.account_history: List[AccountData] = []
        
        # 注册事件处理器
        self.register_event_handlers()
        
        # 引擎参数
        self.commission_rate = 0.0003
        self.slippage_rate = 0.01
        self.size = 1.0

    def register_event_handlers(self):
        """注册事件处理器"""
        self.event_engine.register(EventType.EVENT_TRADE, self.process_trade_event)
        self.event_engine.register(EventType.EVENT_BAR, self.process_bar_event)
        self.event_engine.register(EventType.EVENT_INIT, self.process_init_event)

    def process_init_event(self, event: Event):
        """处理初始化事件"""
        self.account_history.clear()
        self.positions.clear()
        self.trades.clear()
        
        # 重置账户数据
        self.account_data = AccountData(
            datetime=datetime.now(),
            balance=self.initial_capital,
            available=self.initial_capital,
            frozen=0.0,
            total_pnl=0.0,
            realized_pnl=0.0,
            unrealized_pnl=0.0,
            commission=0.0,
            slippage=0.0,
            equity=self.initial_capital,
            margin_used=0.0,
            margin_available=self.initial_capital
        )

    def process_trade_event(self, event: Event):
        """处理交易事件"""
        trade: TradeData = event.data
        self.trades.append(trade)
        
        # 更新持仓
        self.update_position(trade)
        
        # 计算交易成本
        trade_value = trade.price * trade.volume * self.size
        commission = trade_value * self.commission_rate
        slippage = trade.volume * self.size * self.slippage_rate
        
        # 更新累计成本
        self.account_data.commission += commission
        self.account_data.slippage += slippage
        
        # 更新已实现盈亏（如果是平仓交易）
        if trade.offset.value == "平":
            # 计算平仓盈亏
            position_key = f"{trade.symbol}_{trade.direction.value}"
            if position_key in self.positions:
                position = self.positions[position_key]
                if trade.direction == Direction.LONG:
                    pnl = (trade.price - position.price) * trade.volume * self.size
                else:
                    pnl = (position.price - trade.price) * trade.volume * self.size
                
                self.account_data.realized_pnl += pnl - commission - slippage
        
        # 发送账户更新事件
        self.send_account_update_event()

    def process_bar_event(self, event: Event):
        """处理K线事件"""
        bar: BarData = event.data
        
        # 更新账户时间
        self.account_data.datetime = bar.datetime
        
        # 计算未实现盈亏
        self.calculate_unrealized_pnl(bar.close_price)
        
        # 更新账户净值
        self.update_equity()
        
        # 保存历史数据
        self.save_account_history()
        
        # 发送账户更新事件
        self.send_account_update_event()

    def update_position(self, trade: TradeData):
        """更新持仓"""
        position_key = f"{trade.symbol}_{trade.direction.value}"
        
        if position_key not in self.positions:
            # 新建持仓
            self.positions[position_key] = PositionData(
                symbol=trade.symbol,
                direction=trade.direction.value,
                volume=trade.volume,
                price=trade.price,
                pnl=0.0,
                entry_time=trade.datetime
            )
        else:
            # 更新现有持仓
            position = self.positions[position_key]
            if trade.offset.value == "开":
                # 加仓
                total_volume = position.volume + trade.volume
                position.price = (position.price * position.volume + 
                                trade.price * trade.volume) / total_volume
                position.volume = total_volume
            else:
                # 减仓
                position.volume -= trade.volume
                if position.volume <= 0:
                    del self.positions[position_key]

    def calculate_unrealized_pnl(self, current_price: float):
        """计算未实现盈亏"""
        unrealized_pnl = 0.0
        
        for position in self.positions.values():
            if position.direction == "long":
                pnl = (current_price - position.price) * position.volume * self.size
            else:
                pnl = (position.price - current_price) * position.volume * self.size
            
            position.pnl = pnl
            unrealized_pnl += pnl
        
        self.account_data.unrealized_pnl = unrealized_pnl

    def update_equity(self):
        """更新账户净值"""
        self.account_data.total_pnl = (self.account_data.realized_pnl + 
                                     self.account_data.unrealized_pnl)
        self.account_data.equity = (self.initial_capital + 
                                  self.account_data.total_pnl - 
                                  self.account_data.commission - 
                                  self.account_data.slippage)
        
        # 更新可用资金
        margin_used = sum(pos.price * pos.volume for pos in self.positions.values())
        self.account_data.margin_used = margin_used
        self.account_data.available = self.account_data.equity - margin_used

    def save_account_history(self):
        """保存账户历史数据"""
        # 创建当前账户数据的副本
        account_copy = AccountData(
            datetime=self.account_data.datetime,
            balance=self.account_data.balance,
            available=self.account_data.available,
            frozen=self.account_data.frozen,
            total_pnl=self.account_data.total_pnl,
            realized_pnl=self.account_data.realized_pnl,
            unrealized_pnl=self.account_data.unrealized_pnl,
            commission=self.account_data.commission,
            slippage=self.account_data.slippage,
            equity=self.account_data.equity,
            margin_used=self.account_data.margin_used,
            margin_available=self.account_data.margin_available
        )
        self.account_history.append(account_copy)

    def send_account_update_event(self):
        """发送账户更新事件"""
        event = Event(EventType.EVENT_ACCOUNT_UPDATE, self.account_data)
        self.event_engine.put(event)

    def get_account_data(self) -> AccountData:
        """获取当前账户数据"""
        return self.account_data

    def get_positions(self) -> Dict[str, PositionData]:
        """获取当前持仓"""
        return self.positions

    def get_account_history(self) -> List[AccountData]:
        """获取账户历史数据"""
        return self.account_history

    def set_parameters(self, commission_rate: float = None, 
                      slippage_rate: float = None, size: float = None):
        """设置引擎参数"""
        if commission_rate is not None:
            self.commission_rate = commission_rate
        if slippage_rate is not None:
            self.slippage_rate = slippage_rate
        if size is not None:
            self.size = size

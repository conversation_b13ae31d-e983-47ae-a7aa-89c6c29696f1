# 事件驱动架构说明

## 概述

vnpy_backtester现在集成了完整的事件驱动架构，将画图、账户资金管理、统计指标计算等功能都纳入到事件驱动系统中。这种架构提供了更好的模块化、实时性和可扩展性。

## 架构组件

### 1. 事件引擎 (EventEngine)
- **位置**: `vnpy_backtester/utils/event.py`
- **功能**: 事件分发和处理的核心
- **新增事件类型**:
  - `EVENT_ACCOUNT_UPDATE`: 账户更新事件
  - `EVENT_STATISTICS_UPDATE`: 统计指标更新事件
  - `EVENT_CHART_UPDATE`: 图表更新事件
  - `EVENT_EQUITY_UPDATE`: 资金曲线更新事件
  - `EVENT_DRAWDOWN_UPDATE`: 回撤更新事件
  - `EVENT_PERFORMANCE_UPDATE`: 绩效指标更新事件

### 2. 账户管理引擎 (AccountEngine)
- **位置**: `vnpy_backtester/engines/account_engine.py`
- **功能**: 实时跟踪和管理账户资金、持仓、盈亏
- **主要特性**:
  - 实时计算账户净值
  - 跟踪已实现/未实现盈亏
  - 管理保证金和可用资金
  - 记录交易成本（手续费、滑点）
  - 维护持仓信息

### 3. 统计指标引擎 (StatisticsEngine)
- **位置**: `vnpy_backtester/engines/statistics_engine.py`
- **功能**: 实时计算各种统计指标
- **计算指标**:
  - 基础指标：总收益率、年化收益率、最大回撤、夏普比率
  - 交易指标：总交易次数、胜率、盈亏比、利润因子
  - 时间指标：交易天数、盈利天数、亏损天数

### 4. 图表引擎 (ChartEngine)
- **位置**: `vnpy_backtester/engines/chart_engine.py`
- **功能**: 实时生成和更新图表
- **图表功能**:
  - 资金曲线图
  - 回撤曲线图
  - 交易标记点
  - 自动保存和显示

## 事件流程

### 回测过程中的事件流
```
K线数据 → EVENT_BAR → 策略处理 → 发送订单 → EVENT_ORDER
                                    ↓
交易成交 → EVENT_TRADE → 账户更新 → EVENT_ACCOUNT_UPDATE
                                    ↓
统计计算 → EVENT_STATISTICS_UPDATE → 图表更新 → EVENT_CHART_UPDATE
```

### 具体事件处理流程

1. **K线事件处理**:
   ```python
   # 新K线到达
   self.event_engine.put(Event(EventType.EVENT_BAR, bar))
   
   # 账户引擎处理
   def process_bar_event(self, event):
       bar = event.data
       self.calculate_unrealized_pnl(bar.close_price)
       self.update_equity()
       self.send_account_update_event()
   ```

2. **交易事件处理**:
   ```python
   # 交易成交
   self.event_engine.put(Event(EventType.EVENT_TRADE, trade))
   
   # 账户引擎更新持仓和资金
   def process_trade_event(self, event):
       trade = event.data
       self.update_position(trade)
       self.calculate_commission_and_slippage(trade)
       self.send_account_update_event()
   ```

3. **账户更新事件处理**:
   ```python
   # 账户更新
   self.event_engine.put(Event(EventType.EVENT_ACCOUNT_UPDATE, account_data))
   
   # 统计引擎计算指标
   def process_account_update_event(self, event):
       account_data = event.data
       self.update_equity_curve(account_data.equity)
       self.calculate_statistics()
       self.send_statistics_update_event()
   ```

## 使用方法

### 1. 基本使用
```python
from vnpy_backtester.engines.engine import BacktestingEngine

# 创建引擎（自动集成所有事件驱动组件）
engine = BacktestingEngine()

# 设置参数
engine.set_parameters(
    vt_symbol="ETHUSDT.BINANCE",
    start=start_date,
    end=end_date,
    rate=0.0003,
    slippage=0.01,
    capital=50000
)

# 运行回测
engine.run_backtesting()
```

### 2. 获取实时数据
```python
# 获取账户数据
account_data = engine.get_account_data()
print(f"账户净值: {account_data.equity}")
print(f"已实现盈亏: {account_data.realized_pnl}")

# 获取统计数据
stats = engine.get_statistics_from_engines()
print(f"总收益率: {stats['total_return']}")
print(f"夏普比率: {stats['sharpe_ratio']}")

# 获取资金曲线
equity_curve = engine.get_equity_curve()
```

### 3. 生成图表
```python
# 生成并保存图表
fig = engine.generate_chart(
    save_path="charts/result.html",
    show_chart=True,
    title="回测结果"
)
```

## 优势

### 1. 实时性
- 所有指标在K线级别实时计算
- 账户状态实时更新
- 图表数据实时收集

### 2. 模块化
- 各个引擎独立工作
- 通过事件系统松耦合
- 易于扩展和维护

### 3. 一致性
- 实盘和回测使用相同架构
- 数据流向清晰明确
- 减少计算误差

### 4. 可扩展性
- 易于添加新的事件类型
- 可以插入新的处理器
- 支持自定义指标计算

## 测试验证

运行测试脚本验证功能：
```bash
python vnpy_backtester/scripts/test_event_driven_engines.py
```

测试结果显示：
- ✅ 账户管理引擎正常工作
- ✅ 统计指标引擎实时计算
- ✅ 图表引擎成功生成图表
- ✅ 事件系统协调各组件
- ✅ 10种事件类型，22个事件处理器

## 性能对比

| 指标 | 传统方法 | 事件驱动方法 | 改进 |
|------|----------|--------------|------|
| 计算精度 | K线级别 | K线级别 | 一致 |
| 实时性 | 回测结束后 | 实时更新 | ✅ 提升 |
| 内存使用 | 批量计算 | 增量计算 | ✅ 优化 |
| 可扩展性 | 有限 | 高度可扩展 | ✅ 提升 |
| 代码维护 | 耦合度高 | 模块化 | ✅ 提升 |

## 未来扩展

1. **风险管理引擎**: 实时风险监控和控制
2. **绩效归因引擎**: 详细的绩效分析
3. **实时监控面板**: Web界面实时显示
4. **多策略协调**: 策略间的事件通信
5. **机器学习集成**: 实时特征计算和模型更新

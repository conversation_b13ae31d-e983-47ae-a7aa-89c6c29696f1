"""
图表引擎 - 基于事件驱动架构
负责实时生成和更新图表
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
import os

from vnpy_backtester.utils.event import Event, EventEngine, EventType
from vnpy_backtester.engines.account_engine import AccountEngine
from vnpy_backtester.engines.statistics_engine import StatisticsEngine


class ChartEngine:
    """
    图表引擎
    基于事件驱动架构，实时生成和更新图表
    """

    def __init__(self, event_engine: EventEngine, account_engine: AccountEngine, 
                 statistics_engine: StatisticsEngine):
        """
        初始化图表引擎
        
        参数:
        event_engine: 事件引擎
        account_engine: 账户引擎
        statistics_engine: 统计引擎
        """
        self.event_engine = event_engine
        self.account_engine = account_engine
        self.statistics_engine = statistics_engine
        
        # 图表配置
        self.chart_config = {
            'auto_update': True,  # 是否自动更新图表
            'save_path': None,  # 图表保存路径
            'show_chart': False,  # 是否显示图表
            'update_frequency': 100,  # 更新频率（每N个K线更新一次）
        }
        
        # 图表数据
        self.chart_data = {
            'timestamps': [],
            'equity_curve': [],
            'drawdown_curve': [],
            'returns': [],
            'trades': []
        }
        
        # 更新计数器
        self.update_counter = 0
        
        # 注册事件处理器
        self.register_event_handlers()

    def register_event_handlers(self):
        """注册事件处理器"""
        self.event_engine.register(EventType.EVENT_ACCOUNT_UPDATE, self.process_account_update_event)
        self.event_engine.register(EventType.EVENT_STATISTICS_UPDATE, self.process_statistics_update_event)
        self.event_engine.register(EventType.EVENT_TRADE, self.process_trade_event)
        self.event_engine.register(EventType.EVENT_INIT, self.process_init_event)
        self.event_engine.register(EventType.EVENT_STOP, self.process_stop_event)

    def process_init_event(self, event: Event):
        """处理初始化事件"""
        self.chart_data = {
            'timestamps': [],
            'equity_curve': [],
            'drawdown_curve': [],
            'returns': [],
            'trades': []
        }
        self.update_counter = 0

    def process_account_update_event(self, event: Event):
        """处理账户更新事件"""
        account_data = event.data
        
        # 更新图表数据
        self.chart_data['timestamps'].append(account_data.datetime)
        self.chart_data['equity_curve'].append(account_data.equity)
        
        # 计算回撤
        if self.chart_data['equity_curve']:
            peak = max(self.chart_data['equity_curve'])
            current_drawdown = (peak - account_data.equity) / peak * 100 if peak > 0 else 0
            self.chart_data['drawdown_curve'].append(current_drawdown)
        
        # 计算收益率
        if len(self.chart_data['equity_curve']) > 1:
            prev_equity = self.chart_data['equity_curve'][-2]
            current_return = (account_data.equity - prev_equity) / prev_equity * 100
            self.chart_data['returns'].append(current_return)
        
        # 检查是否需要更新图表
        self.update_counter += 1
        if (self.chart_config['auto_update'] and 
            self.update_counter % self.chart_config['update_frequency'] == 0):
            self.update_chart()

    def process_statistics_update_event(self, event: Event):
        """处理统计更新事件"""
        # 可以在这里处理统计指标的图表更新
        pass

    def process_trade_event(self, event: Event):
        """处理交易事件"""
        trade_data = event.data
        
        # 记录交易信息用于图表标记
        trade_info = {
            'datetime': trade_data.datetime,
            'price': trade_data.price,
            'volume': trade_data.volume,
            'direction': trade_data.direction.value,
            'offset': trade_data.offset.value
        }
        self.chart_data['trades'].append(trade_info)

    def process_stop_event(self, event: Event):
        """处理停止事件，生成最终图表"""
        self.generate_final_chart()

    def update_chart(self):
        """更新图表（实时更新）"""
        # 这里可以实现实时图表更新逻辑
        # 由于是回测环境，通常不需要实时显示
        pass

    def generate_final_chart(self):
        """生成最终图表"""
        if not self.chart_data['equity_curve']:
            print("没有数据可用于生成图表")
            return
        
        try:
            import plotly.graph_objects as go
            from plotly.subplots import make_subplots
            
            # 创建子图
            fig = make_subplots(
                rows=2, cols=1,
                subplot_titles=('资金曲线', '回撤曲线'),
                vertical_spacing=0.1,
                row_heights=[0.7, 0.3]
            )
            
            # 添加资金曲线
            fig.add_trace(
                go.Scatter(
                    x=self.chart_data['timestamps'],
                    y=self.chart_data['equity_curve'],
                    mode='lines',
                    name='资金曲线',
                    line=dict(color='blue', width=2)
                ),
                row=1, col=1
            )
            
            # 添加交易标记
            if self.chart_data['trades']:
                buy_trades = [t for t in self.chart_data['trades'] 
                            if t['direction'] == 'LONG' and t['offset'] == '开']
                sell_trades = [t for t in self.chart_data['trades'] 
                             if t['direction'] == 'SHORT' and t['offset'] == '开']
                
                if buy_trades:
                    fig.add_trace(
                        go.Scatter(
                            x=[t['datetime'] for t in buy_trades],
                            y=[self.get_equity_at_time(t['datetime']) for t in buy_trades],
                            mode='markers',
                            name='买入',
                            marker=dict(color='green', size=8, symbol='triangle-up')
                        ),
                        row=1, col=1
                    )
                
                if sell_trades:
                    fig.add_trace(
                        go.Scatter(
                            x=[t['datetime'] for t in sell_trades],
                            y=[self.get_equity_at_time(t['datetime']) for t in sell_trades],
                            mode='markers',
                            name='卖出',
                            marker=dict(color='red', size=8, symbol='triangle-down')
                        ),
                        row=1, col=1
                    )
            
            # 添加回撤曲线
            if self.chart_data['drawdown_curve']:
                fig.add_trace(
                    go.Scatter(
                        x=self.chart_data['timestamps'][:len(self.chart_data['drawdown_curve'])],
                        y=[-dd for dd in self.chart_data['drawdown_curve']],  # 负值显示
                        mode='lines',
                        name='回撤',
                        line=dict(color='red', width=1),
                        fill='tonexty',
                        fillcolor='rgba(255,0,0,0.3)'
                    ),
                    row=2, col=1
                )
            
            # 更新布局
            fig.update_layout(
                title='回测结果分析',
                height=800,
                showlegend=True,
                hovermode='x unified'
            )
            
            # 更新x轴
            fig.update_xaxes(title_text="时间", row=2, col=1)
            
            # 更新y轴
            fig.update_yaxes(title_text="资金", row=1, col=1)
            fig.update_yaxes(title_text="回撤 (%)", row=2, col=1)
            
            # 保存图表
            if self.chart_config['save_path']:
                self.save_chart(fig, self.chart_config['save_path'])
            
            # 显示图表
            if self.chart_config['show_chart']:
                fig.show(renderer="browser")
            
            # 发送图表更新事件
            event = Event(EventType.EVENT_CHART_UPDATE, fig)
            self.event_engine.put(event)
            
            return fig
            
        except ImportError:
            print("Plotly未安装，无法生成图表")
            return None
        except Exception as e:
            print(f"生成图表时出错: {e}")
            return None

    def get_equity_at_time(self, target_time: datetime) -> float:
        """获取指定时间的资金值"""
        for i, timestamp in enumerate(self.chart_data['timestamps']):
            if timestamp >= target_time:
                return self.chart_data['equity_curve'][i] if i < len(self.chart_data['equity_curve']) else 0
        return self.chart_data['equity_curve'][-1] if self.chart_data['equity_curve'] else 0

    def save_chart(self, fig, save_path: str):
        """保存图表"""
        try:
            # 确保目录存在
            save_dir = os.path.dirname(save_path)
            if save_dir and not os.path.exists(save_dir):
                os.makedirs(save_dir, exist_ok=True)
            
            # 保存为HTML文件
            fig.write_html(
                save_path,
                config={
                    'responsive': True,
                    'scrollZoom': True,
                    'displayModeBar': True,
                    'displaylogo': False,
                }
            )
            print(f"图表已保存为 {save_path}")
            return True
            
        except Exception as e:
            print(f"保存图表时出错: {e}")
            return False

    def set_config(self, **kwargs):
        """设置图表配置"""
        for key, value in kwargs.items():
            if key in self.chart_config:
                self.chart_config[key] = value

    def get_chart_data(self) -> Dict[str, Any]:
        """获取图表数据"""
        return self.chart_data.copy()

    def create_custom_chart(self, title: str = "自定义图表", **kwargs):
        """创建自定义图表"""
        try:
            import plotly.graph_objects as go
            
            fig = go.Figure()
            
            # 添加资金曲线
            if self.chart_data['equity_curve']:
                fig.add_trace(
                    go.Scatter(
                        x=self.chart_data['timestamps'],
                        y=self.chart_data['equity_curve'],
                        mode='lines',
                        name='资金曲线',
                        line=dict(color='blue', width=2)
                    )
                )
            
            # 更新布局
            fig.update_layout(
                title=title,
                xaxis_title="时间",
                yaxis_title="资金",
                height=600,
                showlegend=True,
                **kwargs
            )
            
            return fig
            
        except ImportError:
            print("Plotly未安装，无法创建图表")
            return None
        except Exception as e:
            print(f"创建图表时出错: {e}")
            return None
